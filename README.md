# AI Code Stats

一个用于统计 Git 提交中 AI 生成代码的工具。该工具可以自动识别和统计代码中由 AI 生成的部分，并提供详细的统计报告。

## 功能特性

- 📊 **详细统计**: 统计代码变更、AI 代码行数、文件数量等信息
- 🔄 **自动化**: 支持 Git hooks，在代码推送时自动执行统计
- 🌐 **远程上报**: 可配置将统计数据发送到远程 API
- 📝 **详细报告**: 提供控制台输出和结构化数据输出

## 安装

### 全局安装
```bash
npm install -g ai-code-stats
```

### 本地安装
```bash
npm install ai-code-stats
```

### 开发环境安装
```bash
# 克隆仓库
git clone <repository-url>
cd ai-code-stats

# 安装依赖（包括测试依赖）
npm install

# 验证安装和测试环境
npm test basic.test.js
```

## 使用方法

### 1. 基本使用

统计最新提交的 AI 代码：
```bash
ai-code-stats
```

统计指定提交的 AI 代码：
```bash
ai-code-stats <commit-hash>
```

指定输出模式：
```bash
ai-code-stats <commit-hash> detailed  # 详细模式（默认）
ai-code-stats <commit-hash> summary   # 简洁模式
```

### 3. 清理 AI 标记

统计完成后清理所有 AI 标记（推荐在提交前使用）：
```bash
cleanup-ai-markers
```

或者使用 npm script：
```bash
npm run cleanup
```

静默模式（不显示详细信息）：
```bash
cleanup-ai-markers --quiet
```

### 2. 安装 Git Hooks

#### 方式一：使用 Husky（推荐）

使用 Husky 管理 Git hooks，在每次提交前自动统计 AI 代码并清理标记：
```bash
install-husky-hooks
```

或者使用 npm script：
```bash
npm run install-husky
```

#### 方式二：传统 Git Hooks

自动在每次推送时统计 AI 代码并清理标记：
```bash
install-ai-stats-hook
```

或者使用 npm script：
```bash
npm run install-hooks
```

### 3. AI 代码标记

在你的代码中使用以下标记来标识 AI 生成的代码：

```javascript
function aiGeneratedFunction() {
    // 这里是 AI 生成的代码
    return "Hello from AI";
}
```

```python
def ai_generated_function():
    """这是 AI 生成的函数"""
    return "Hello from AI"
```

```html
<div class="ai-generated-component">
    <h1>AI 生成的组件</h1>
</div>
```

## 配置

### API 配置

编辑 `ai-code-stats.js` 中的 CONFIG 对象来配置远程 API：

```javascript
const CONFIG = {
  // 远程API配置
  api: {
    url: 'https://your-api-endpoint.com/stats',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your-api-key-here'
    }
  },
  // 是否在控制台输出详细信息
  verbose: true,
  // 是否在推送时发送统计数据
  sendOnPush: true,
  // 是否在统计完成后清理AI标记
  cleanupMarkers: true
};
```

## 输出示例

```
===== 代码提交统计 =====
仓库: my-project (main)
提交: a1b2c3d4
作者: Robin Deng <<EMAIL>>
时间: 2024-01-15T10:30:00.000Z
消息: 添加新功能

代码变更:
- 修改文件数: 3
- 添加行数: 150
- 删除行数: 20
- 净变更: 130

AI生成代码:
- 包含AI代码的文件数: 2
- AI生成的总代码行数: 85
- AI代码占比: 56.67%

统计数据已成功发送到远程API
```

## 数据结构

工具输出的统计数据结构如下：

```json
{
  "repository": {
    "repoUrl": "https://github.com/user/repo.git",
    "repoName": "repo",
    "branchName": "main"
  },
  "commit": {
    "hash": "a1b2c3d4e5f6...",
    "author": "Robin Deng <<EMAIL>>",
    "message": "提交信息",
    "date": "2024-01-15T10:30:00.000Z"
  },
  "codeChanges": {
    "files": 3,
    "totalAdded": 150,
    "totalDeleted": 20,
    "netChange": 130,
    "details": [...]
  },
  "aiCode": {
    "filesWithAICode": 2,
    "totalAILines": 85,
    "details": [...]
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "event": "push"
}
```

## Git Hooks

### Husky 集成（推荐）

使用 Husky 管理 Git hooks，提供更好的开发体验：

#### 安装和配置

1. **安装 Husky hooks**：
```bash
install-husky-hooks
```

2. **工作流程**：
   - 开发时在代码中添加 `[[GBAI START]]` 和 `[[GBAI END]]` 标记
   - 使用 `git add` 添加文件（包含 AI 标记）
   - 使用 `git commit` 提交时自动触发：
     - 统计 staged 文件中的 AI 代码
     - 清理工作目录中的 AI 标记
     - 重新 stage 清理后的文件
     - 完成提交（提交的代码已清理标记）

3. **优势**：
   - ✅ **无额外提交**: 用户的提交直接包含清理后的代码
   - ✅ **实时统计**: 基于即将提交的代码进行统计
   - ✅ **快速通过**: 没有 AI 标记时快速通过，不影响提交速度
   - ✅ **标准化**: 使用业界标准的 Husky 管理 hooks

#### 配置文件

创建 `.ai-code-stats.json` 来自定义行为：
```json
{
  "enabled": true,
  "autoCleanup": true,
  "verbose": true,
  "hooks": {
    "preCommit": true,
    "prePush": false
  }
}
```

### 传统 Git Hooks

安装后，工具会在 `.git/hooks/pre-push` 创建一个钩子，该钩子会：

1. **智能检测推送范围**: 自动识别当前 push 包含的新提交，避免重复统计历史提交
2. **新分支处理**: 对于新分支，只统计当前提交而不是所有历史提交
3. **现有分支处理**: 对于现有分支，只统计本地与远程的差异提交
4. **对每个新提交执行 AI 代码统计**（基于包含标记的原始代码）
5. **自动清理 AI 标记**: 统计完成后清理工作目录中的所有 AI 标记
6. **自动创建清理提交**: 如果有标记被清理，自动创建一个清理提交
7. **推送干净代码**: 确保推送到远程仓库的代码不包含 AI 标记
8. **输出详细统计结果**
9. **可选择性地将数据发送到远程 API**

### Hook 工作原理

- **新分支推送**: 只统计当前 HEAD 提交
- **现有分支推送**: 统计 `remote_sha..local_sha` 范围内的所有新提交
- **统计阶段**: 基于包含 AI 标记的原始代码进行统计
- **清理阶段**: 统计完成后自动清理工作目录中的 AI 标记
- **提交阶段**: 如果有标记被清理，自动创建清理提交
- **推送阶段**: 推送包含清理提交的干净代码

### 推送示例

```bash
# 场景1: 推送新分支（单个提交）
git push origin feature-branch
# 输出: 检测到新分支推送: refs/heads/feature-branch
#       正在统计提交: a1b2c3d4
#       ===== 代码提交统计 =====
#       [详细统计信息...]

# 场景2: 推送单个新提交
git push origin main
# 输出: 检测到分支更新: refs/heads/main
#       正在统计提交: e5f6g7h8
#       ===== 代码提交统计 =====
#       [详细统计信息...]

# 场景3: 推送多个新提交
git push origin main
# 输出: 检测到分支更新: refs/heads/main
#       检测到 3 个新提交，开始统计...
#
#       [a1b2c3d4] 添加用户认证功能
#         代码变更: +120/-15 (5个文件)
#         AI代码: 85行 (70.8%) 在3个文件中
#       [e5f6g7h8] 修复登录bug
#         代码变更: +8/-3 (2个文件)
#         AI代码: 无
#       [i9j0k1l2] 更新文档
#         代码变更: +25/-0 (1个文件)
#         AI代码: 无
#
#       ===== 本次推送汇总统计 =====
#       总计 3 个提交
#       代码变更: +153/-18 (8 个文件)
#       时间范围: 2024-01-15 09:30:00 到 2024-01-15 10:30:00

# 场景4: 推送后自动清理
git push origin main
# 输出: 检测到分支更新: refs/heads/main
#       正在统计提交: a1b2c3d4
#       [详细统计信息...]
#
#       ===== 清理AI标记 =====
#       清理了 2 个文件中的 6 个AI标记
#       ✅ 已创建清理提交

# 场景5: 没有新提交
git push origin main
# 输出: 没有新的提交需要统计
```

## 工作流程说明

### 完整的开发流程

1. **开发阶段**: 在代码中添加 AI 标记来标识 AI 生成的代码
2. **提交阶段**: 正常使用 `git commit` 提交代码（包含 AI 标记）
3. **推送阶段**: 使用 `git push` 时自动触发：
   - 统计 AI 代码（基于包含标记的代码）
   - 清理工作目录中的 AI 标记
   - 自动创建清理提交
   - 推送干净的代码到远程仓库

### 优势

- **无需手动清理**: 推送时自动处理
- **统计准确**: 基于包含标记的原始代码统计
- **代码干净**: 远程仓库中的代码不包含 AI 标记
- **历史清晰**: 清理作为独立提交，便于追踪

## AI 标记清理

### 自动清理（推荐）

当启用 Git Hooks 时，工具会在推送时自动清理所有 AI 标记：

```
===== 清理AI标记 =====
清理了 2 个文件中的 6 个AI标记
✅ 已创建清理提交
```

### 手动清理

你也可以随时手动清理 AI 标记：

```bash
# 详细模式
cleanup-ai-markers

# 输出示例:
# 开始清理AI标记...
#
# ✓ src/components/UserAuth.js (移除 2 个标记)
# ✓ src/utils/helpers.js (移除 4 个标记)
#
# ===== 清理完成 =====
# ✅ 成功清理了 2 个文件中的 6 个AI标记
#
# 处理的文件列表:
#   - src/components/UserAuth.js: 2 个标记
#   - src/utils/helpers.js: 4 个标记
#
# 💡 提示: 现在可以安全地提交代码，不会包含AI标记

# 静默模式
cleanup-ai-markers --quiet
```

### 清理原理

- 扫描当前目录下所有文件（排除 `.git/`、`node_modules/` 等）
- 保持文件的其他内容不变
- 支持所有文本文件格式

## 开发

### 项目结构

```
ai-code-stats/
├── lib/                      # 核心功能模块
│   ├── ai-code-stats.js     # 主要统计逻辑
│   ├── install-hooks.js     # Git hooks 安装脚本
│   ├── install-husky-hooks.js # Husky hooks 安装脚本
│   └── cleanup-markers.js   # AI标记清理脚本
├── package.json             # 项目配置
├── README.md                # 项目文档
```

### 核心功能

- **getRepoInfo()**: 获取仓库基本信息
- **getCommitInfo()**: 获取提交详细信息
- **getCodeStats()**: 统计代码变更
- **getAICodeStats()**: 统计 AI 生成的代码
- **cleanupAIMarkers()**: 清理工作目录中的 AI 标记
- **sendStatsToAPI()**: 发送数据到远程 API